
import React, { useRef, useState, useEffect } from "react";
import { useCart } from "../context/CartContext";
import { useAuth } from "../context/AuthContext";
import axios from "axios";
import { toast } from "sonner";

const Discover = () => {
  const scrollRef = useRef(null);
  const { addItem } = useCart();
  const { isAuthenticated } = useAuth();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch products from server
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        // First try to get products
        let response = await axios.get("http://localhost:5000/api/products");

        // If no products, seed the database
        if (response.data.length === 0) {
          await axios.post("http://localhost:5000/api/products/seed");
          response = await axios.get("http://localhost:5000/api/products");
        }

        setProducts(response.data);
      } catch (error) {
        console.error("Error fetching products:", error);
        toast.error("Failed to load products");

        // Fallback to sample products if server fails
        setProducts([
          {
            _id: "1",
            name: "AUTOMATIQUE PLATINUM JAPAN EDITION",
            price: 89000,
            currency: "USD",
            image: "https://images.unsplash.com/photo-1600003014755-ba31aa59c4b6?fm=jpg&q=80&w=1000&ixlib=rb-4.1.0",
            description: "Limited edition platinum timepiece"
          },
          {
            _id: "2",
            name: "AUTOMATIQUE ROSE GOLD",
            price: 75000,
            currency: "USD",
            image: "https://images.unsplash.com/photo-1604242692760-2f7b0c26856d?fm=jpg&q=80&w=1000&ixlib=rb-4.1.0",
            description: "Classic rose gold masterpiece"
          },
          {
            _id: "3",
            name: "AUTOMATIQUE CARBON EDITION",
            price: 65000,
            currency: "USD",
            image: "https://images.unsplash.com/photo-1634595947394-87012e7b12ba?fm=jpg&q=80&w=1000&ixlib=rb-4.1.0",
            description: "Modern carbon fiber design"
          },
          {
            _id: "4",
            name: "AUTOMATIQUE TITANIUM BLACK",
            price: 58000,
            currency: "USD",
            image: "https://images.unsplash.com/photo-1548171916-c0dea7f94ca6?fm=jpg&q=80&w=1000&ixlib=rb-4.1.0",
            description: "Lightweight titanium construction"
          },
          {
            _id: "5",
            name: "AUTOMATIQUE SKELETON",
            price: 95000,
            currency: "USD",
            image: "https://images.unsplash.com/photo-1548171838-1fd4cb4ab854?fm=jpg&q=80&w=1000&ixlib=rb-4.1.0",
            description: "Transparent mechanics showcase"
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  const scroll = (direction) => {
    if (scrollRef.current) {
      const scrollAmount = 400; // Adjust this value based on your card width
      scrollRef.current.scrollLeft +=
        direction === "left" ? -scrollAmount : scrollAmount;
    }
  };

  return (
    <div className="text-white pt-[15vh] min-h-[100vh] w-[100vw] overflow-hidden bg-[#0c0c0c]">
      <div className="container relative">
        <div className="flex flex-row items-center justify-between p-10">
          <span className="font-[f2] text-2xl">DISCOVER OUR WATCHES </span>
          <a
            style={{ textDecoration: "underline" }}
            className="font-[f4] text-xl"
          >
            View more
          </a>
        </div>

        {/* Cards container */}
        <div
          ref={scrollRef}
          className="flex gap-8 pb-30 pt-[10vh] pl-10 overflow-x-auto scroll-smooth hide-scrollbar"
        >
          {loading ? (
            <div className="flex justify-center items-center w-full py-20">
              <p className="text-xl">Loading products...</p>
            </div>
          ) : (
            products.map((product) => (
              <div
                key={product._id}
                className="flex flex-col flex-shrink-0 w-[80vw] sm:w-[50vw] md:w-[40vw] lg:w-[30vw] xl:w-[24vw] rounded-lg shadow-sm bg-[#0c0c0c] mb-8"
              >
                <a className="w-full" href="#">
                  <img
                    className="h-[24vh] w-full px-6 border-1 border-gray-600 object-contain"
                    src={product.image}
                    alt={product.name}
                  />
                </a>
                <div className="p-5 overflow-hidden">
                  <a href="#">
                    <h5 className="font-[f4] mb-2 text-xl tracking-tight text-gray-900 dark:text-white">
                      LANDMARK TIMEPIECES
                    </h5>
                  </a>
                  <p className="font-[f2] pl-[3vh] flex justify-start items-center text-center text-[3vh] mb-6">
                    {product.name.split(' ').map((word, i) => (
                      <React.Fragment key={i}>
                        {word} <br />
                      </React.Fragment>
                    ))}
                  </p>
                  <button
                    onClick={() => {
                      // Convert MongoDB _id to id for client-side use
                      const clientProduct = {
                        ...product,
                        id: product._id
                      };
                      addItem(clientProduct);
                    }}
                    style={{ textDecoration: "underline" }}
                    className="font-[f4] text-xl flex justify-start pl-[6vh] hover:text-gray-300 transition-colors"
                  >
                    {isAuthenticated ? "Add to Cart" : "Login to Add"}
                  </button>
                </div>
              </div>
            ))
          )}

          {/* Scroll buttons */}
          <div className="absolute top-[90vh] left-[45vw] z-10 flex gap-4">
            <button
              onClick={() => scroll("left")}
              className="bg-white rounded-full w-3 h-3 flex items-center justify-center hover:opacity-80 transition-opacity"
            >
              ←
            </button>
            <button
              onClick={() => scroll("right")}
              className="bg-white rounded-full w-3 h-3 flex items-center justify-center hover:opacity-80 transition-opacity"
            >
              →
            </button>
          </div>
        </div>
      </div>

      <style jsx="true">{`
        .hide-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .hide-scrollbar::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
};

export default Discover;



















// import React, { useRef } from "react";

// const Discover = () => {
//   const scrollRef = useRef(null);
//   const products = [1, 2, 3, 4, 5]; // Sample array of products

//   const scroll = (direction) => {
//     if (scrollRef.current) {
//       const scrollAmount = 400; // Adjust this value based on your card width
//       scrollRef.current.scrollLeft +=
//         direction === "left" ? -scrollAmount : scrollAmount;
//     }
//   };

//   return (
//     <div className="text-white pt-[15vh] min-h-[100vh] w-[100vw] overflow-hidden bg-[#0c0c0c]">
//       <div className="container relative">
//         <div className="flex flex-row items-center justify-between p-10">
//           <span className="font-[f2] text-2xl">DISCOVER OUR WATCHES </span>
//           <a
//             style={{ textDecoration: "underline" }}
//             className="font-[f4] text-xl"
//           >
//             View more
//           </a>
//         </div>

//         {/* Cards container */}
//         <div
//           ref={scrollRef}
//           className="flex gap-8 pb-30 pt-[10vh] pl-10 overflow-x-auto scroll-smooth hide-scrollbar"
//         >
//           {products.map((product, index) => (
//             <div
//               key={index}
//               className="flex flex-col flex-shrink-0 w-[80vw] sm:w-[50vw] md:w-[40vw] lg:w-[30vw] xl:w-[24vw] rounded-lg shadow-sm bg-[#0c0c0c] mb-8"
//             >
//               <a className="w-full" href="#">
//                 <img
//                   className="h-[24vh] w-full px-6 border-1 border-gray-600 object-contain"
//                   src="https://jcbiver.com/images/uploads/automatique-obsidian_hu2746300296472835155.webp"
//                   alt="Watch"
//                 />
//               </a>
//               <div className="p-5 overflow-hidden">
//                 <a href="#">
//                   <h5 className="font-[f4] mb-2 text-xl tracking-tight text-gray-900 dark:text-white">
//                     LANDMARK TIMEPIECES
//                   </h5>
//                 </a>
//                 <p className="font-[f2] pl-[3vh] flex justify-start items-center text-center text-[3vh] mb-6">
//                   AUTOMATIQUE <br /> PLATINUM <br /> JAPAN EDITION
//                 </p>
//                 <a
//                   style={{ textDecoration: "underline" }}
//                   className="font-[f4] text-xl flex justify-start pl-[6vh]"
//                 >
//                   Add to Cart
//                 </a>
//               </div>

//             </div>

//           ))}
//           {/* Scroll buttons */}
//           <div className="absolute  top-[90vh] left-[45vw] z-10 flex gap-4 ">
//                 <button
//                   onClick={() => scroll("left")}
//                   className="bg-white rounded-full w-3 h-3 flex items-center justify-center hover:opacity-80 transition-opacity"
//                 >
//                   ←
//                 </button>
//                 <button
//                   onClick={() => scroll("right")}
//                   className="bg-white rounded-full w-3 h-3 flex items-center justify-center hover:opacity-80 transition-opacity"
//                 >
//                   →
//                 </button>
//               </div>
//         </div>
//       </div>

//       <style jsx>{`
//         .hide-scrollbar {
//           -ms-overflow-style: none;
//           scrollbar-width: none;
//         }
//         .hide-scrollbar::-webkit-scrollbar {
//           display: none;
//         }
//       `}</style>
//     </div>
//   );
// };

// export default Discover;
